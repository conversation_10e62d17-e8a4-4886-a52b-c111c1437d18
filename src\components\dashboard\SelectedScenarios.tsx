import React, { useState } from 'react';
import { ChevronDown, Eye, BarChart3, Calendar, DollarSign, TrendingUp, Download, Percent, AlertTriangle, Bookmark, X, FileText, PieChart, LineChart, AreaChart, FileDown, Mail, MessageCircle, Share2 } from 'lucide-react';
import { LineChart as RechartsLineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, BarChart as RechartsBar<PERSON>hart, Bar, AreaChart as RechartsAreaChart, Area, PieChart as RechartsPieChart, Cell, Pie } from 'recharts';
import Card from '../common/Card';
import Button from '../common/Button';
import { useDashboard } from '../../contexts/DashboardContext';
import type { Scenario as BaseScenario } from '../../types';

type ScenarioWithKeyPoints = BaseScenario & { keyPoints?: string[] };

const SelectedScenarios: React.FC = () => {
  const { scenarios, selectedCustomerData, selectedPolicyData, setActiveTab } = useDashboard();
  const [selectedScenario, setSelectedScenario] = useState<string | null>(null);
  const [showVisualization, setShowVisualization] = useState(false);
  const [showExportPopup, setShowExportPopup] = useState(false);
  const [expandedKeyPoints, setExpandedKeyPoints] = useState<string | null>(null);
  const [selectedCharts, setSelectedCharts] = useState({
    line: true,
    bar: true,
    area: true,
    pie: true
  });

  // Chart colors
  const chartColors = {
    plannedPremium: '#3B82F6',
    netOutlay: '#10B981',
    netSurrenderValue: '#8B5CF6',
    netDeathBenefit: '#F59E0B'
  };









  // Use only real scenarios from the database - no mock data
  const displayScenarios: ScenarioWithKeyPoints[] = scenarios as ScenarioWithKeyPoints[];

  const handleScenarioClick = (scenarioId: string) => {
    setSelectedScenario(selectedScenario === scenarioId ? null : scenarioId);
  };

  // Prepare chart data for visualization
  const prepareChartData = (tableData: any[]) => {
    return tableData.map(row => ({
      year: row.policyYear,
      age: row.endOfAge,
      'Planned Premium': row.plannedPremium,
      'Net Outlay': row.netOutlay,
      'Net Surrender Value': row.netSurrenderValue,
      'Net Death Benefit': row.netDeathBenefit
    }));
  };

  // Prepare pie chart data for the latest year
  const preparePieData = (tableData: any[]) => {
    const latestData = tableData[tableData.length - 1];
    return [
      { name: 'Planned Premium', value: latestData.plannedPremium, color: chartColors.plannedPremium },
      { name: 'Net Outlay', value: latestData.netOutlay, color: chartColors.netOutlay },
      { name: 'Net Surrender Value', value: latestData.netSurrenderValue, color: chartColors.netSurrenderValue },
      { name: 'Net Death Benefit', value: latestData.netDeathBenefit, color: chartColors.netDeathBenefit }
    ];
  };

  // Custom tooltip formatter
  const formatTooltip = (value: any, name: string) => {
    return [`$${value.toLocaleString()}`, name];
  };

  // Handle chart selection toggle
  const toggleChart = (chartType: keyof typeof selectedCharts) => {
    setSelectedCharts(prev => ({
      ...prev,
      [chartType]: !prev[chartType]
    }));
  };

  // Select all charts
  const selectAllCharts = () => {
    setSelectedCharts({
      line: true,
      bar: true,
      area: true,
      pie: true
    });
  };

  // Clear all charts
  const clearAllCharts = () => {
    setSelectedCharts({
      line: false,
      bar: false,
      area: false,
      pie: false
    });
  };

  // Individual chart rendering functions
  const renderLineChart = (data: any[]) => {
    return (
      <ResponsiveContainer width="100%" height={400}>
        <RechartsLineChart data={data} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
          <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
          <XAxis
            dataKey="year"
            className="text-sm"
            tick={{ fontSize: 12 }}
          />
          <YAxis
            className="text-sm"
            tick={{ fontSize: 12 }}
            tickFormatter={(value) => `$${(value / 1000).toFixed(0)}K`}
          />
          <Tooltip
            formatter={formatTooltip}
            labelFormatter={(label) => `Year: ${label}`}
            contentStyle={{
              backgroundColor: 'rgba(255, 255, 255, 0.95)',
              border: '1px solid #e5e7eb',
              borderRadius: '8px',
              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
            }}
          />
          <Legend />
          <Line
            type="monotone"
            dataKey="Planned Premium"
            stroke={chartColors.plannedPremium}
            strokeWidth={3}
            dot={{ fill: chartColors.plannedPremium, strokeWidth: 2, r: 4 }}
            activeDot={{ r: 6, stroke: chartColors.plannedPremium, strokeWidth: 2 }}
          />
          <Line
            type="monotone"
            dataKey="Net Outlay"
            stroke={chartColors.netOutlay}
            strokeWidth={3}
            dot={{ fill: chartColors.netOutlay, strokeWidth: 2, r: 4 }}
            activeDot={{ r: 6, stroke: chartColors.netOutlay, strokeWidth: 2 }}
          />
          <Line
            type="monotone"
            dataKey="Net Surrender Value"
            stroke={chartColors.netSurrenderValue}
            strokeWidth={3}
            dot={{ fill: chartColors.netSurrenderValue, strokeWidth: 2, r: 4 }}
            activeDot={{ r: 6, stroke: chartColors.netSurrenderValue, strokeWidth: 2 }}
          />
          <Line
            type="monotone"
            dataKey="Net Death Benefit"
            stroke={chartColors.netDeathBenefit}
            strokeWidth={3}
            dot={{ fill: chartColors.netDeathBenefit, strokeWidth: 2, r: 4 }}
            activeDot={{ r: 6, stroke: chartColors.netDeathBenefit, strokeWidth: 2 }}
          />
        </RechartsLineChart>
      </ResponsiveContainer>
    );
  };

  const renderBarChart = (data: any[]) => {
    return (
      <ResponsiveContainer width="100%" height={400}>
        <RechartsBarChart data={data} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
          <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
          <XAxis
            dataKey="year"
            className="text-sm"
            tick={{ fontSize: 12 }}
          />
          <YAxis
            className="text-sm"
            tick={{ fontSize: 12 }}
            tickFormatter={(value) => `$${(value / 1000).toFixed(0)}K`}
          />
          <Tooltip
            formatter={formatTooltip}
            labelFormatter={(label) => `Year: ${label}`}
            contentStyle={{
              backgroundColor: 'rgba(255, 255, 255, 0.95)',
              border: '1px solid #e5e7eb',
              borderRadius: '8px',
              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
            }}
          />
          <Legend />
          <Bar dataKey="Planned Premium" fill={chartColors.plannedPremium} radius={[2, 2, 0, 0]} />
          <Bar dataKey="Net Outlay" fill={chartColors.netOutlay} radius={[2, 2, 0, 0]} />
          <Bar dataKey="Net Surrender Value" fill={chartColors.netSurrenderValue} radius={[2, 2, 0, 0]} />
          <Bar dataKey="Net Death Benefit" fill={chartColors.netDeathBenefit} radius={[2, 2, 0, 0]} />
        </RechartsBarChart>
      </ResponsiveContainer>
    );
  };

  const renderAreaChart = (data: any[]) => {
    return (
      <ResponsiveContainer width="100%" height={400}>
        <RechartsAreaChart data={data} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
          <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
          <XAxis
            dataKey="year"
            className="text-sm"
            tick={{ fontSize: 12 }}
          />
          <YAxis
            className="text-sm"
            tick={{ fontSize: 12 }}
            tickFormatter={(value) => `$${(value / 1000).toFixed(0)}K`}
          />
          <Tooltip
            formatter={formatTooltip}
            labelFormatter={(label) => `Year: ${label}`}
            contentStyle={{
              backgroundColor: 'rgba(255, 255, 255, 0.95)',
              border: '1px solid #e5e7eb',
              borderRadius: '8px',
              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
            }}
          />
          <Legend />
          <Area
            type="monotone"
            dataKey="Net Death Benefit"
            stackId="1"
            stroke={chartColors.netDeathBenefit}
            fill={chartColors.netDeathBenefit}
            fillOpacity={0.6}
          />
          <Area
            type="monotone"
            dataKey="Net Surrender Value"
            stackId="1"
            stroke={chartColors.netSurrenderValue}
            fill={chartColors.netSurrenderValue}
            fillOpacity={0.6}
          />
          <Area
            type="monotone"
            dataKey="Net Outlay"
            stackId="1"
            stroke={chartColors.netOutlay}
            fill={chartColors.netOutlay}
            fillOpacity={0.6}
          />
          <Area
            type="monotone"
            dataKey="Planned Premium"
            stackId="1"
            stroke={chartColors.plannedPremium}
            fill={chartColors.plannedPremium}
            fillOpacity={0.6}
          />
        </RechartsAreaChart>
      </ResponsiveContainer>
    );
  };

  const renderPieChart = (pieData: any[]) => {
    return (
      <ResponsiveContainer width="100%" height={400}>
        <RechartsPieChart>
          <Pie
            data={pieData}
            cx="50%"
            cy="50%"
            labelLine={false}
            label={({ name, percent }) => `${name}: ${((percent || 0) * 100).toFixed(1)}%`}
            outerRadius={120}
            fill="#8884d8"
            dataKey="value"
          >
            {pieData.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={entry.color} />
            ))}
          </Pie>
          <Tooltip formatter={(value) => `$${value.toLocaleString()}`} />
          <Legend />
        </RechartsPieChart>
      </ResponsiveContainer>
    );
  };



  // Generate dynamic table data based on scenario
  const generateTableData = (scenario: ScenarioWithKeyPoints) => {
    const baseData = [
      { policyYear: 2025, endOfAge: 40, plannedPremium: 10000, netOutlay: 5000, netSurrenderValue: 50000, netDeathBenefit: 250000 },
      { policyYear: 2026, endOfAge: 41, plannedPremium: 10000, netOutlay: 5500, netSurrenderValue: 51000, netDeathBenefit: 255000 },
      { policyYear: 2027, endOfAge: 42, plannedPremium: 10000, netOutlay: 6000, netSurrenderValue: 52000, netDeathBenefit: 260000 },
      { policyYear: 2028, endOfAge: 43, plannedPremium: 10000, netOutlay: 6500, netSurrenderValue: 53000, netDeathBenefit: 265000 },
      { policyYear: 2029, endOfAge: 44, plannedPremium: 10000, netOutlay: 7000, netSurrenderValue: 54000, netDeathBenefit: 270000 },
      { policyYear: 2030, endOfAge: 45, plannedPremium: 10000, netOutlay: 7500, netSurrenderValue: 55000, netDeathBenefit: 275000 },
      { policyYear: 2031, endOfAge: 46, plannedPremium: 10000, netOutlay: 8000, netSurrenderValue: 56000, netDeathBenefit: 280000 },
      { policyYear: 2032, endOfAge: 47, plannedPremium: 10000, netOutlay: 8500, netSurrenderValue: 57000, netDeathBenefit: 285000 },
      { policyYear: 2033, endOfAge: 48, plannedPremium: 10000, netOutlay: 9000, netSurrenderValue: 58000, netDeathBenefit: 290000 },
      { policyYear: 2034, endOfAge: 49, plannedPremium: 10000, netOutlay: 9500, netSurrenderValue: 59000, netDeathBenefit: 295000 },
    ];

    // Modify data based on scenario category
    return baseData.map((row, index) => {
      let modifiedRow = { ...row };

      switch (scenario.category) {
        case 'face-amount':
          modifiedRow.netDeathBenefit = row.netDeathBenefit * 1.2; // 20% increase
          modifiedRow.plannedPremium = row.plannedPremium * 1.1; // 10% increase
          break;
        case 'premium':
          modifiedRow.plannedPremium = row.plannedPremium * 0.8; // 20% decrease
          modifiedRow.netOutlay = row.netOutlay * 0.85; // 15% decrease
          break;
        case 'income':
          modifiedRow.netSurrenderValue = row.netSurrenderValue * 1.15; // 15% increase
          modifiedRow.netDeathBenefit = row.netDeathBenefit * 1.1; // 10% increase
          break;
        case 'loan-repayment':
          modifiedRow.netOutlay = row.netOutlay * 1.3; // 30% increase
          modifiedRow.netSurrenderValue = row.netSurrenderValue * 0.9; // 10% decrease
          break;
        case 'interest-rate':
          const multiplier = 1 + (index * 0.02); // Compound growth
          modifiedRow.netSurrenderValue = Math.round(row.netSurrenderValue * multiplier);
          modifiedRow.netDeathBenefit = Math.round(row.netDeathBenefit * multiplier);
          break;

        default: // as-is
          // Keep original values
          break;
      }

      return modifiedRow;
    });
  };



  const selectedScenarioData = selectedScenario ? 
    displayScenarios.find(s => s.id === selectedScenario) : null;



  return (
    <div className="space-y-6">
      {/* No Scenarios Saved Message */}
      {displayScenarios.length === 0 ? (
        <Card className="bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-yellow-100 dark:bg-yellow-900/40 rounded-full flex items-center justify-center">
              <span className="text-yellow-600 dark:text-yellow-400 text-lg font-bold">!</span>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-yellow-800 dark:text-yellow-200">No Scenarios Saved</h3>
              <p className="text-yellow-700 dark:text-yellow-300 mb-3">
                No scenarios have been saved yet. Go to Policy Illustration to create and select scenarios.
              </p>
              <Button
                variant="primary"
                onClick={() => setActiveTab('illustrations')}
              >
                Policy Illustration
              </Button>
            </div>
          </div>
        </Card>
      ) : (
        /* Policy Overview Section */
        <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border-blue-200 dark:border-blue-800">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Policy Overview</h3>
            <span className="px-3 py-1 bg-green-100 dark:bg-green-800 text-green-800 dark:text-green-100 rounded-full text-sm font-medium">
              Active
            </span>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Policy Holder</p>
              <p className="text-lg font-semibold text-gray-900 dark:text-white">
                {selectedCustomerData?.name}
              </p>
            </div>
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Policy Number</p>
              <p className="text-lg font-semibold text-gray-900 dark:text-white">
                {selectedCustomerData?.policyNumber}
              </p>
            </div>
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Customer ID</p>
              <p className="text-lg font-semibold text-gray-900 dark:text-white">
                {selectedCustomerData?.customerId}
              </p>
            </div>
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Policy Type</p>
              <p className="text-lg font-semibold text-gray-900 dark:text-white">
                {selectedPolicyData?.name}
              </p>
            </div>
          </div>
          <div className="mt-4 pt-4 border-t border-blue-200 dark:border-blue-700">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Face Amount</p>
                <p className="text-xl font-bold text-blue-600 dark:text-blue-400">
                  ${parseInt(selectedPolicyData.coverage).toLocaleString()}
                </p>
              </div>
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Annual Premium</p>
                <p className="text-xl font-bold text-green-600 dark:text-green-400">
                  {selectedPolicyData.premium}
                </p>
              </div>
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Total Scenarios</p>
                <p className="text-xl font-bold text-purple-600 dark:text-purple-400">{displayScenarios.length}</p>
              </div>
            </div>
          </div>
        </Card>
      )}

      {/* Scenarios Container */}
      {displayScenarios.length > 0 && (
        <Card className="p-4 overflow-hidden shadow-lg max-w-5xl mx-auto">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {displayScenarios.map((scenario) => (
            <div
              key={scenario.id}
              onClick={() => handleScenarioClick(scenario.id)}
              className="bg-white dark:bg-gray-800 rounded-lg border-2 border-gray-100 dark:border-gray-700 p-5 cursor-pointer hover:border-blue-300 dark:hover:border-blue-500 hover:shadow-lg transition-all duration-200 group"
            >
              {/* Simplified Header */}
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-3">
                  {(() => {
                    // Determine icon and color based on category
                    let icon = Bookmark;
                    let color = 'from-slate-500 to-slate-600';

                    switch(scenario.category) {
                      case 'as-is':
                        icon = Bookmark;
                        color = 'from-slate-500 to-slate-600';
                        break;
                      case 'face-amount':
                        icon = DollarSign;
                        color = 'from-emerald-500 to-emerald-600';
                        break;
                      case 'premium':
                        icon = TrendingUp;
                        color = 'from-blue-500 to-blue-600';
                        break;
                      case 'income':
                        icon = Download;
                        color = 'from-purple-500 to-purple-600';
                        break;
                      case 'loan-repayment':
                        icon = Percent;
                        color = 'from-orange-500 to-orange-600';
                        break;
                      case 'interest-rate':
                        icon = Percent;
                        color = 'from-indigo-500 to-indigo-600';
                        break;
                      case 'policy-lapse':
                        icon = AlertTriangle;
                        color = 'from-red-500 to-red-600';
                        break;
                    }

                    return React.createElement(icon, {
                      className: `w-6 h-6 p-1 rounded-md bg-gradient-to-r ${color} text-white`
                    });
                  })()}
                  <div>
                    <h4 className="font-bold text-gray-900 dark:text-white text-lg">
                      {scenario.name}
                    </h4>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      {(() => {
                        switch(scenario.category) {
                          case 'as-is': return 'AS-IS';
                          case 'face-amount': return 'Face Amount';
                          case 'premium': return 'Premium';
                          case 'income': return 'Income';
                          case 'loan-repayment': return 'Loan Repayment';
                          case 'interest-rate': return 'Interest Rate';
                          case 'policy-lapse': return 'Policy Lapse';
                          default: return scenario.category;
                        }
                      })()}
                    </p>
                  </div>
                </div>
                <div className="text-blue-500 group-hover:text-blue-600 transition-colors">
                  <Eye className="w-5 h-5" />
                </div>
              </div>

              {/* Key Points */}
              {scenario.keyPoints && scenario.keyPoints.length > 0 && (
                <div className="mb-4">
                  <h5 className="text-xs font-semibold text-gray-700 dark:text-gray-300 mb-2 uppercase tracking-wide">Key Points</h5>
                  <div className="text-sm text-gray-600 dark:text-gray-300 space-y-1">
                    {(expandedKeyPoints === scenario.id ? scenario.keyPoints : scenario.keyPoints.slice(0, 3)).map((point, index) => (
                      <div key={index} className="flex items-start space-x-2">
                        <div className="w-1.5 h-1.5 bg-blue-500 rounded-full mt-1.5 flex-shrink-0" />
                        <span className="text-xs leading-relaxed">{point}</span>
                      </div>
                    ))}
                    {scenario.keyPoints.length > 3 && (
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          setExpandedKeyPoints(expandedKeyPoints === scenario.id ? null : scenario.id);
                        }}
                        className="text-blue-600 dark:text-blue-400 text-xs font-medium ml-4 hover:text-blue-700 dark:hover:text-blue-300 transition-colors"
                      >
                        {expandedKeyPoints === scenario.id
                          ? 'Show less'
                          : `+${scenario.keyPoints.length - 3} more points`
                        }
                      </button>
                    )}
                  </div>
                </div>
              )}

              {/* Simplified Footer */}
              <div className="flex items-center justify-between pt-3 border-t border-gray-200 dark:border-gray-600">
                <div className="flex items-center space-x-1 text-gray-400 dark:text-gray-500">
                  <Calendar className="w-4 h-4" />
                  <span className="text-xs">{scenario.createdAt.toLocaleDateString()}</span>
                </div>
                <div className="text-blue-600 dark:text-blue-400 text-sm font-medium opacity-0 group-hover:opacity-100 transition-opacity">
                  View →
                </div>
              </div>
            </div>
          ))}


        </div>
      </Card>
      )}

      {/* Inline Scenario Details and Table */}
      {selectedScenario && selectedScenarioData && (
        <div className="mt-8 space-y-6">




          {/* Key Performance Metrics */}
          <Card>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Key Performance Metrics</h3>
            {(() => {
              const tableData = generateTableData(selectedScenarioData);

              const lastYear = tableData[tableData.length - 1];
              const totalPremiums = tableData.reduce((sum, row) => sum + row.plannedPremium, 0);
              const totalOutlay = tableData.reduce((sum, row) => sum + row.netOutlay, 0);
              const finalSurrenderValue = lastYear.netSurrenderValue;
              const finalDeathBenefit = lastYear.netDeathBenefit;
              const netGain = finalSurrenderValue - totalOutlay;
              const roi = totalOutlay > 0 ? ((netGain / totalOutlay) * 100) : 0;

              return (
                <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
                  <div className="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/30 p-4 rounded-lg border border-blue-200 dark:border-blue-700">
                    <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                      ${totalPremiums.toLocaleString()}
                    </div>
                    <div className="text-sm text-blue-700 dark:text-blue-300 font-medium">Total Premiums</div>
                  </div>
                  <div className="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/30 p-4 rounded-lg border border-green-200 dark:border-green-700">
                    <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                      ${totalOutlay.toLocaleString()}
                    </div>
                    <div className="text-sm text-green-700 dark:text-green-300 font-medium">Total Outlay</div>
                  </div>
                  <div className="bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/30 p-4 rounded-lg border border-purple-200 dark:border-purple-700">
                    <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">
                      ${finalSurrenderValue.toLocaleString()}
                    </div>
                    <div className="text-sm text-purple-700 dark:text-purple-300 font-medium">Final Cash Value</div>
                  </div>
                  <div className="bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-900/20 dark:to-orange-800/30 p-4 rounded-lg border border-orange-200 dark:border-orange-700">
                    <div className="text-2xl font-bold text-orange-600 dark:text-orange-400">
                      ${finalDeathBenefit.toLocaleString()}
                    </div>
                    <div className="text-sm text-orange-700 dark:text-orange-300 font-medium">Final Death Benefit</div>
                  </div>
                  <div className={`bg-gradient-to-br p-4 rounded-lg border ${
                    netGain >= 0
                      ? 'from-emerald-50 to-emerald-100 dark:from-emerald-900/20 dark:to-emerald-800/30 border-emerald-200 dark:border-emerald-700'
                      : 'from-red-50 to-red-100 dark:from-red-900/20 dark:to-red-800/30 border-red-200 dark:border-red-700'
                  }`}>
                    <div className={`text-2xl font-bold ${
                      netGain >= 0
                        ? 'text-emerald-600 dark:text-emerald-400'
                        : 'text-red-600 dark:text-red-400'
                    }`}>
                      ${netGain.toLocaleString()}
                    </div>
                    <div className={`text-sm font-medium ${
                      netGain >= 0
                        ? 'text-emerald-700 dark:text-emerald-300'
                        : 'text-red-700 dark:text-red-300'
                    }`}>
                      Net {netGain >= 0 ? 'Gain' : 'Loss'}
                    </div>
                  </div>
                  <div className={`bg-gradient-to-br p-4 rounded-lg border ${
                    roi >= 0
                      ? 'from-teal-50 to-teal-100 dark:from-teal-900/20 dark:to-teal-800/30 border-teal-200 dark:border-teal-700'
                      : 'from-red-50 to-red-100 dark:from-red-900/20 dark:to-red-800/30 border-red-200 dark:border-red-700'
                  }`}>
                    <div className={`text-2xl font-bold ${
                      roi >= 0
                        ? 'text-teal-600 dark:text-teal-400'
                        : 'text-red-600 dark:text-red-400'
                    }`}>
                      {roi.toFixed(1)}%
                    </div>
                    <div className={`text-sm font-medium ${
                      roi >= 0
                        ? 'text-teal-700 dark:text-teal-300'
                        : 'text-red-700 dark:text-red-300'
                    }`}>
                      ROI
                    </div>
                  </div>
                </div>
              );
            })()}
          </Card>

          {/* Enhanced Illustration Table */}
          <Card>
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl font-bold text-gray-900 dark:text-white flex items-center">
                <BarChart3 className="w-6 h-6 text-blue-600 mr-2" />
                Projection Analysis
              </h3>
              <div className="text-sm text-gray-500 dark:text-gray-400">
                10-Year Illustration
              </div>
            </div>
            <div className="overflow-x-auto rounded-lg border border-gray-200 dark:border-gray-700">
              <table className="w-full">
                <thead className="bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-700">
                  <tr>
                    <th className="px-4 py-4 text-left font-bold text-gray-700 dark:text-gray-200 text-sm">#</th>
                    <th className="px-4 py-4 text-left font-bold text-gray-700 dark:text-gray-200 text-sm">Year</th>
                    <th className="px-4 py-4 text-left font-bold text-gray-700 dark:text-gray-200 text-sm">Age</th>
                    <th className="px-4 py-4 text-right font-bold text-gray-700 dark:text-gray-200 text-sm">Premium</th>
                    <th className="px-4 py-4 text-right font-bold text-gray-700 dark:text-gray-200 text-sm">Net Outlay</th>
                    <th className="px-4 py-4 text-right font-bold text-gray-700 dark:text-gray-200 text-sm">Cash Value</th>
                    <th className="px-4 py-4 text-right font-bold text-gray-700 dark:text-gray-200 text-sm">Death Benefit</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                  {generateTableData(selectedScenarioData).map((row, index) => (
                    <tr key={index} className="hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                      <td className="px-4 py-3 text-gray-700 dark:text-gray-200 font-medium text-sm">{index + 1}</td>
                      <td className="px-4 py-3 text-gray-700 dark:text-gray-200 font-semibold">{row.policyYear}</td>
                      <td className="px-4 py-3 text-gray-700 dark:text-gray-200 font-medium">{row.endOfAge}</td>
                      <td className="px-4 py-3 text-right text-gray-700 dark:text-gray-200 font-bold">${row.plannedPremium.toLocaleString()}</td>
                      <td className="px-4 py-3 text-right text-gray-700 dark:text-gray-200 font-bold">${row.netOutlay.toLocaleString()}</td>
                      <td className="px-4 py-3 text-right text-gray-700 dark:text-gray-200 font-bold">${row.netSurrenderValue.toLocaleString()}</td>
                      <td className="px-4 py-3 text-right text-gray-700 dark:text-gray-200 font-bold">${row.netDeathBenefit.toLocaleString()}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </Card>

          {/* Get Illustration and Visualization Options */}
          <Card>
            <div className="flex items-center justify-center gap-6">
              {/* Get Illustration Button */}
              <button
                onClick={() => setShowExportPopup(true)}
                className="flex items-center space-x-2 px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors font-medium shadow-md"
              >
                <Download className="w-5 h-5" />
                <span>Get Illustration</span>
              </button>

              {/* Data Visualization Button */}
              <button
                onClick={() => setShowVisualization(!showVisualization)}
                className="flex items-center space-x-2 px-5 py-2.5 bg-indigo-600 hover:bg-indigo-700 text-white rounded-lg transition-colors font-medium"
              >
                <BarChart3 className="w-4 h-4" />
                <span>{showVisualization ? 'Hide Charts' : 'Show Charts'}</span>
              </button>
            </div>
          </Card>

          {/* Export Options Popup */}
          {showExportPopup && (
            <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
              <div className="bg-white dark:bg-gray-800 rounded-xl shadow-2xl p-6 w-96 max-w-md mx-4">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-xl font-bold text-gray-900 dark:text-white">Get Illustration</h3>
                  <button
                    onClick={() => setShowExportPopup(false)}
                    className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
                  >
                    <X className="w-6 h-6" />
                  </button>
                </div>

                <div className="space-y-3">
                  <button className="w-full flex items-center space-x-3 px-4 py-3 text-left hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors group">
                    <FileText className="w-5 h-5 text-green-600 group-hover:text-green-700" />
                    <div>
                      <span className="text-gray-900 dark:text-white font-medium">Export CSV</span>
                      <div className="text-sm text-gray-500">Download as spreadsheet</div>
                    </div>
                  </button>

                  <button className="w-full flex items-center space-x-3 px-4 py-3 text-left hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors group">
                    <FileDown className="w-5 h-5 text-red-600 group-hover:text-red-700" />
                    <div>
                      <span className="text-gray-900 dark:text-white font-medium">Export PDF</span>
                      <div className="text-sm text-gray-500">Download as document</div>
                    </div>
                  </button>

                  <button className="w-full flex items-center space-x-3 px-4 py-3 text-left hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors group">
                    <Mail className="w-5 h-5 text-blue-600 group-hover:text-blue-700" />
                    <div>
                      <span className="text-gray-900 dark:text-white font-medium">Email</span>
                      <div className="text-sm text-gray-500">Send via email</div>
                    </div>
                  </button>

                  <button className="w-full flex items-center space-x-3 px-4 py-3 text-left hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors group">
                    <MessageCircle className="w-5 h-5 text-green-600 group-hover:text-green-700" />
                    <div>
                      <span className="text-gray-900 dark:text-white font-medium">WhatsApp</span>
                      <div className="text-sm text-gray-500">Share instantly</div>
                    </div>
                  </button>

                  <button className="w-full flex items-center space-x-3 px-4 py-3 text-left hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors group">
                    <Share2 className="w-5 h-5 text-purple-600 group-hover:text-purple-700" />
                    <div>
                      <span className="text-gray-900 dark:text-white font-medium">Share</span>
                      <div className="text-sm text-gray-500">Share with others</div>
                    </div>
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Visualization Section */}
          {showVisualization && (
            <div className="space-y-6">
              <Card>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-6">Interactive Data Visualization</h3>

              {/* Chart Selection Checkboxes */}
              <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4 mb-6">
                <div className="flex items-center justify-between mb-3">
                  <h4 className="text-sm font-semibold text-gray-900 dark:text-white">Select Charts to Display:</h4>
                  <div className="flex space-x-2">
                    <button
                      onClick={selectAllCharts}
                      className="text-xs px-3 py-1 bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200 transition-colors"
                    >
                      Select All
                    </button>
                    <button
                      onClick={clearAllCharts}
                      className="text-xs px-3 py-1 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors"
                    >
                      Clear All
                    </button>
                  </div>
                </div>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <label className="flex items-center space-x-3 cursor-pointer group">
                    <input
                      type="checkbox"
                      checked={selectedCharts.line}
                      onChange={() => toggleChart('line')}
                      className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"
                    />
                    <div className="flex items-center space-x-2">
                      <LineChart className="w-4 h-4 text-blue-600" />
                      <span className="text-sm font-medium text-gray-900 dark:text-white group-hover:text-blue-600">Line Chart</span>
                    </div>
                  </label>

                  <label className="flex items-center space-x-3 cursor-pointer group">
                    <input
                      type="checkbox"
                      checked={selectedCharts.bar}
                      onChange={() => toggleChart('bar')}
                      className="w-4 h-4 text-green-600 bg-gray-100 border-gray-300 rounded focus:ring-green-500 focus:ring-2"
                    />
                    <div className="flex items-center space-x-2">
                      <BarChart3 className="w-4 h-4 text-green-600" />
                      <span className="text-sm font-medium text-gray-900 dark:text-white group-hover:text-green-600">Bar Chart</span>
                    </div>
                  </label>

                  <label className="flex items-center space-x-3 cursor-pointer group">
                    <input
                      type="checkbox"
                      checked={selectedCharts.area}
                      onChange={() => toggleChart('area')}
                      className="w-4 h-4 text-purple-600 bg-gray-100 border-gray-300 rounded focus:ring-purple-500 focus:ring-2"
                    />
                    <div className="flex items-center space-x-2">
                      <AreaChart className="w-4 h-4 text-purple-600" />
                      <span className="text-sm font-medium text-gray-900 dark:text-white group-hover:text-purple-600">Area Chart</span>
                    </div>
                  </label>

                  <label className="flex items-center space-x-3 cursor-pointer group">
                    <input
                      type="checkbox"
                      checked={selectedCharts.pie}
                      onChange={() => toggleChart('pie')}
                      className="w-4 h-4 text-orange-600 bg-gray-100 border-gray-300 rounded focus:ring-orange-500 focus:ring-2"
                    />
                    <div className="flex items-center space-x-2">
                      <PieChart className="w-4 h-4 text-orange-600" />
                      <span className="text-sm font-medium text-gray-900 dark:text-white group-hover:text-orange-600">Pie Chart</span>
                    </div>
                  </label>
                </div>
              </div>

              {/* Charts Grid */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {(() => {
                  const tableData = generateTableData(selectedScenarioData);
                  const chartData = prepareChartData(tableData);
                  const pieData = preparePieData(tableData);
                  const charts = [];

                  if (selectedCharts.line) {
                    charts.push(
                      <Card key="line" className="p-6">
                        <div className="mb-4">
                          <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-2 flex items-center">
                            <LineChart className="w-5 h-5 text-blue-600 mr-2" />
                            Line Chart - Trend Analysis
                          </h4>
                          <p className="text-gray-600 dark:text-gray-400 text-sm">
                            Shows the progression of policy values over time with smooth trend lines
                          </p>
                        </div>
                        {renderLineChart(chartData)}
                      </Card>
                    );
                  }

                  if (selectedCharts.bar) {
                    charts.push(
                      <Card key="bar" className="p-6">
                        <div className="mb-4">
                          <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-2 flex items-center">
                            <BarChart3 className="w-5 h-5 text-green-600 mr-2" />
                            Bar Chart - Value Comparison
                          </h4>
                          <p className="text-gray-600 dark:text-gray-400 text-sm">
                            Compare different policy values side by side for each year
                          </p>
                        </div>
                        {renderBarChart(chartData)}
                      </Card>
                    );
                  }

                  if (selectedCharts.area) {
                    charts.push(
                      <Card key="area" className="p-6">
                        <div className="mb-4">
                          <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-2 flex items-center">
                            <AreaChart className="w-5 h-5 text-purple-600 mr-2" />
                            Area Chart - Cumulative Growth
                          </h4>
                          <p className="text-gray-600 dark:text-gray-400 text-sm">
                            Visualize the cumulative growth and stacking of policy values
                          </p>
                        </div>
                        {renderAreaChart(chartData)}
                      </Card>
                    );
                  }

                  if (selectedCharts.pie) {
                    charts.push(
                      <Card key="pie" className="p-6">
                        <div className="mb-4">
                          <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-2 flex items-center">
                            <PieChart className="w-5 h-5 text-orange-600 mr-2" />
                            Pie Chart - Final Year Distribution
                          </h4>
                          <p className="text-gray-600 dark:text-gray-400 text-sm">
                            Distribution of policy values in the final projection year
                          </p>
                        </div>
                        {renderPieChart(pieData)}
                      </Card>
                    );
                  }

                  return charts.length > 0 ? charts : (
                    <div className="col-span-full">
                      <Card className="p-12">
                        <div className="text-center">
                          <BarChart3 className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                          <h4 className="text-xl font-semibold text-gray-700 dark:text-gray-300 mb-2">No Charts Selected</h4>
                          <p className="text-gray-600 dark:text-gray-400">
                            Please select at least one chart type from the options above to view your data visualization.
                          </p>
                        </div>
                      </Card>
                    </div>
                  );
                })()}
              </div>
            </Card>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default SelectedScenarios;