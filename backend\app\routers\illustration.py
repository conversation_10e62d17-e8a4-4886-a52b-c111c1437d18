from http.client import HTT<PERSON><PERSON>x<PERSON>
from urllib import request
from fastapi import APIRouter
from fastapi.responses import JSONResponse
from app.schema import SelectedIllustrationRequest, SelectedOptionRequest, StoreIllustrationOptionsRequest, StoreIllustrationOptionsResponse
from app.services import Illustration_services
from app.services.Illustration_services import get_full_illustration_options
import traceback
from fastapi import APIRouter
from app.services.Illustration_services import (
    get_full_illustration_options,
    store_options_by_scenario,
)

router = APIRouter()
selected_illustrations_routes = APIRouter()


@router.get("/illustration/options")
def get_combined_illustration_tree(policy_id: int):
    try:
        data = Illustration_services.get_full_illustration_options(policy_id)
        return {"status": "success", "policy_id": policy_id, "data": data}
    except Exception as e:
        traceback.print_exc()
        return JSONResponse(status_code=500, content={"status": "error", "message": str(e)})
    


@router.post("/api/illustration/store_options", response_model=StoreIllustrationOptionsResponse)
def store_selected_options(request: StoreIllustrationOptionsRequest):
    try:
        result = store_options_by_scenario(
            policy_id=request.policyId,
            selected_options=[{"scenarioId": opt.scenarioId, "value": opt.value} for opt in request.selectedIllustrationOptions]
        )
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

    
