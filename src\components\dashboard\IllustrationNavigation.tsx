import React from 'react';
import {
  Bookmark,
  DollarSign,
  TrendingUp,
  Download,
  Percent
} from 'lucide-react';
import { useDashboard } from '../../contexts/DashboardContext';

interface IllustrationTab {
  id: string;
  label: string;
  icon: React.ComponentType<any>;
  description: string;
}

const IllustrationNavigation: React.FC = () => {
  const { activeTab, setActiveTab } = useDashboard();

  const illustrationTabs: IllustrationTab[] = [
    {
      id: 'selected-scenarios',
      label: 'Selected Scenarios',
      icon: Bookmark,
      description: 'View all selected scenarios grouped by illustration'
    },
    {
      id: 'as-is',
      label: 'AS-IS',
      icon: Bookmark,
      description: 'Current policy baseline illustration'
    },
    {
      id: 'face-amount',
      label: 'Face Amount',
      icon: DollarSign,
      description: 'Death benefit amount scenarios'
    },
    {
      id: 'premium',
      label: 'Premium',
      icon: TrendingUp,
      description: 'Premium payment scenarios'
    },
    {
      id: 'interest-rate',
      label: 'Interest Rate',
      icon: Percent,
      description: 'Interest rate based scenarios'
    },
    {
      id: 'income',
      label: 'Full Surrender / Income (Loan & Withdrawal)',
      icon: Download,
      description: 'Loan & withdrawal scenarios'
    },
    {
      id: 'loan-repayment',
      label: 'Loan Repayment',
      icon: Percent,
      description: 'Loan repayment scenarios'
    }
  ];

  const handleTabClick = (tabId: string) => {
    setActiveTab(tabId);
  };

  return (
    <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 shadow-sm">
      <div className="px-6 py-4">
        
        {/* Horizontal Navigation Tabs */}
        <div className="flex flex-wrap gap-2 overflow-x-auto">
          {illustrationTabs.map((tab) => {
            const Icon = tab.icon;
            const isActive = activeTab === tab.id;
            
            return (
              <button
                key={tab.id}
                onClick={() => handleTabClick(tab.id)}
                className={`
                  flex items-center space-x-2 px-4 py-3 rounded-lg text-sm font-medium
                  transition-all duration-200 whitespace-nowrap min-w-fit group relative
                  ${isActive
                    ? 'bg-gradient-to-r from-blue-600 to-blue-500 text-white shadow-lg transform scale-105'
                    : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 dark:hover:from-gray-600 dark:hover:to-gray-600 hover:text-blue-700 dark:hover:text-blue-300'
                  }
                `}
                title={tab.description}
              >
                <Icon className={`w-4 h-4 ${isActive ? 'text-white' : 'group-hover:text-blue-600 dark:group-hover:text-blue-400'} transition-colors`} />
                <span className="font-semibold">{tab.label}</span>
                {isActive && (
                  <div className="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-2 h-2 bg-white rounded-full shadow-md"></div>
                )}
              </button>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default IllustrationNavigation;
