import React, { useState } from 'react';
import Card from '../common/Card';
import Button from '../common/Button';
import Select from '../common/Select';
import Input from '../common/Input';
import { RotateCcw, Save, ArrowRight } from 'lucide-react';
import { useDashboard } from '../../contexts/DashboardContext';

const IncomePage: React.FC = () => {
  // State for each scenario section
  const [fullSurrender, setFullSurrender] = useState({
    enabled: false,
    surrenderNow: false,
    surrenderYear: '',
    surrenderAge: '',
  });

  const [withdrawalModel, setWithdrawalModel] = useState({
    enabled: false,
    fixedAmount: '',
    percentOfCash: '',
    flatAnnualAmount: '',
    flatAnnualStartAge: '',
    scheduleType: '',
    increasingStartAmount: '',
    increasingPercentage: '',
    increasingStartAge: '',
    increasingEndAge: '',
  });

  const [loanModel, setLoanModel] = useState({
    enabled: false,
    fixedAmount: '',
    percentOfCash: '',
    annualAmount: '',
    annualStartAge: '',
    scheduleType: '',
    premiumFinancing: {
      enabled: false,
      loanInterestPayment: false,
      capitalizeLoanInterest: false,
      payOutOfPocket: false,
    },
  });

  const { selectedCustomerData, selectedPolicyData, setActiveTab } = useDashboard();

  // Placeholder for schedule button
  const handleSchedule = (section: string) => {
    alert(`Open schedule editor for: ${section}`);
  };

  // Save all scenarios (placeholder)
  const handleSaveAll = () => {
    alert('All selected income scenarios saved!');
  };

  // Reset all scenario state
  const handleResetScenarios = () => {
    setFullSurrender({
      enabled: false,
      surrenderNow: false,
      surrenderYear: '',
      surrenderAge: '',
    });

    setWithdrawalModel({
      enabled: false,
      fixedAmount: '',
      percentOfCash: '',
      flatAnnualAmount: '',
      flatAnnualStartAge: '',
      scheduleType: '',
      increasingStartAmount: '',
      increasingPercentage: '',
      increasingStartAge: '',
      increasingEndAge: '',
    });

    setLoanModel({
      enabled: false,
      fixedAmount: '',
      percentOfCash: '',
      annualAmount: '',
      annualStartAge: '',
      scheduleType: '',
      premiumFinancing: {
        enabled: false,
        loanInterestPayment: false,
        capitalizeLoanInterest: false,
        payOutOfPocket: false,
      },
    });

    alert('All income scenarios have been reset!');
  };



  return (
    <div className="space-y-6">
      {/* Title and Stepper/Flow Indicator */}

      {/* Show message if no policy is selected */}
      {(!selectedCustomerData || !selectedPolicyData) ? (
        <Card className="bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-yellow-100 dark:bg-yellow-900/40 rounded-full flex items-center justify-center">
              <span className="text-yellow-600 dark:text-yellow-400 text-sm">!</span>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-yellow-800 dark:text-yellow-200">No Policy Selected</h3>
              <p className="text-yellow-700 dark:text-yellow-300">
                Please go to the Policy Selection tab first to search and select a customer policy before configuring the Income illustration.
              </p>
              <Button
                onClick={() => setActiveTab('policy-selection')}
                variant="outline"
                className="mt-3 border-yellow-300 text-yellow-700 hover:bg-yellow-100 dark:border-yellow-600 dark:text-yellow-300 dark:hover:bg-yellow-900/40"
              >
                Go to Policy Selection
              </Button>
            </div>
          </div>
        </Card>
      ) : (
        <div className="min-h-screen bg-gray-50 p-4 space-y-8">
          {/* 1. Full Surrender Section */}
          <Card className="mb-8">
            <h2 className="text-xl font-bold mb-4 text-black">1. Income by full surrender now or chosen year or age</h2>
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center space-x-2">
                  <span className="text-black">Policy Year:</span>
                  <Input
                    value={fullSurrender.surrenderYear}
                    onChange={e => setFullSurrender(prev => ({ ...prev, surrenderYear: e.target.value }))}
                    className="text-black placeholder-black w-24"
                    placeholder="Year"
                  />
                  <span className="text-black">/ Age:</span>
                  <Input
                    value={fullSurrender.surrenderAge}
                    onChange={e => setFullSurrender(prev => ({ ...prev, surrenderAge: e.target.value }))}
                    className="text-black placeholder-black w-24"
                    placeholder="Age"
                  />
                </div>
              </div>
            </div>
          </Card>

          {/* 2. Withdrawal Section */}
          <Card className="mb-8">
            <h2 className="text-xl font-bold mb-4 text-black">2. Do you want to model policy withdrawals/Income (either one-time or recurring)?</h2>
            <div className="space-y-4">
              <div className="flex items-center space-x-4">
                <input type="checkbox" checked={withdrawalModel.enabled} onChange={e => setWithdrawalModel(prev => ({ ...prev, enabled: e.target.checked }))} />
                <span className="font-semibold text-black">Yes, Model different types of withdrawal to illustrate:</span>
              </div>
              {withdrawalModel.enabled && (
                <div className="space-y-4 pl-6">
                  {/* Fixed amount or percentage */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <Input
                      label="Fixed amount: $"
                      value={withdrawalModel.fixedAmount}
                      onChange={e => setWithdrawalModel(prev => ({ ...prev, fixedAmount: e.target.value }))}
                      className="text-black placeholder-black"
                      placeholder="Amount"
                    />
                    <div className="flex items-center space-x-2">
                      <span className="text-black">or % of available cash value:</span>
                      <Input
                        value={withdrawalModel.percentOfCash}
                        onChange={e => setWithdrawalModel(prev => ({ ...prev, percentOfCash: e.target.value }))}
                        className="text-black placeholder-black w-20"
                        placeholder="%"
                      />
                      <span className="text-black">% now</span>
                    </div>
                  </div>

                  {/* Flat Annual Amount */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="flex items-center space-x-2">
                      <span className="text-black">Flat Annual Amount: $</span>
                      <Input
                        value={withdrawalModel.flatAnnualAmount}
                        onChange={e => setWithdrawalModel(prev => ({ ...prev, flatAnnualAmount: e.target.value }))}
                        className="text-black placeholder-black"
                        placeholder="Amount"
                      />
                      <span className="text-black">/year starting now for up to age</span>
                    </div>
                    <Select
                      label="Age"
                      value={withdrawalModel.flatAnnualStartAge}
                      onChange={e => setWithdrawalModel(prev => ({ ...prev, flatAnnualStartAge: e.target.value }))}
                      options={Array.from({length: 50}, (_, i) => ({ value: (65 + i).toString(), label: (65 + i).toString() }))}
                      className="text-black"
                    />
                  </div>

                  {/* Schedule as */}
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <span className="text-black">Schedule as</span>
                      <Button onClick={() => handleSchedule('Withdrawal Schedule')} className="ml-2">Schedule</Button>
                    </div>
                  </div>

                  {/* Increasing Income stream */}
                  <div className="space-y-2">
                    <div className="text-black font-semibold">Increasing Income stream</div>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="flex items-center space-x-2">
                        <span className="text-black">Starting amount</span>
                        <Input
                          value={withdrawalModel.increasingStartAmount}
                          onChange={e => setWithdrawalModel(prev => ({ ...prev, increasingStartAmount: e.target.value }))}
                          className="text-black placeholder-black"
                          placeholder="Amount"
                        />
                      </div>
                      <div className="flex items-center space-x-2">
                        <span className="text-black">by</span>
                        <Input
                          value={withdrawalModel.increasingPercentage}
                          onChange={e => setWithdrawalModel(prev => ({ ...prev, increasingPercentage: e.target.value }))}
                          className="text-black placeholder-black w-20"
                          placeholder="%"
                        />
                        <span className="text-black">% every year from now up to age</span>
                      </div>
                      <Select
                        label="End Age"
                        value={withdrawalModel.increasingEndAge}
                        onChange={e => setWithdrawalModel(prev => ({ ...prev, increasingEndAge: e.target.value }))}
                        options={Array.from({length: 50}, (_, i) => ({ value: (65 + i).toString(), label: (65 + i).toString() }))}
                        className="text-black"
                      />
                    </div>
                  </div>
                </div>
              )}
              <div className="flex items-center space-x-4 mt-2">
                <input type="checkbox" checked={!withdrawalModel.enabled} onChange={e => setWithdrawalModel(prev => ({ ...prev, enabled: !e.target.checked }))} />
                <span className="font-semibold text-black">No, do not model policy withdrawals.</span>
              </div>
            </div>
          </Card>

          {/* 3. Policy Loan Section */}
          <Card className="mb-8">
            <h2 className="text-xl font-bold mb-4 text-black">3. Your Policy is eligible for a loan. Do you want to model an income stream using policy loans (e.g., tax-free retirement income strategy)?</h2>
            <div className="space-y-4">
              <div className="flex items-center space-x-4">
                <input type="checkbox" checked={loanModel.enabled} onChange={e => setLoanModel(prev => ({ ...prev, enabled: e.target.checked }))} />
                <span className="font-semibold text-black">Do you want us to illustrate?</span>
              </div>
              {loanModel.enabled && (
                <div className="space-y-4 pl-6">
                  {/* Fixed amount or percentage */}
                  <div className="space-y-2">
                    <div className="text-black font-semibold">a. Fixed amount or percentage:</div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <Input
                        label="Fixed amount: $"
                        value={loanModel.fixedAmount}
                        onChange={e => setLoanModel(prev => ({ ...prev, fixedAmount: e.target.value }))}
                        className="text-black placeholder-black"
                        placeholder="Amount"
                      />
                      <div className="flex items-center space-x-2">
                        <span className="text-black">or % of available cash value:</span>
                        <Input
                          value={loanModel.percentOfCash}
                          onChange={e => setLoanModel(prev => ({ ...prev, percentOfCash: e.target.value }))}
                          className="text-black placeholder-black w-20"
                          placeholder="%"
                        />
                        <span className="text-black">% now</span>
                      </div>
                    </div>
                  </div>

                  {/* Annual Amount */}
                  <div className="space-y-2">
                    <div className="text-black font-semibold">b. Annual Amount:</div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="flex items-center space-x-2">
                        <span className="text-black">$</span>
                        <Input
                          value={loanModel.annualAmount}
                          onChange={e => setLoanModel(prev => ({ ...prev, annualAmount: e.target.value }))}
                          className="text-black placeholder-black"
                          placeholder="Amount"
                        />
                        <span className="text-black">/year starting now for up to age</span>
                      </div>
                      <Select
                        label="Age"
                        value={loanModel.annualStartAge}
                        onChange={e => setLoanModel(prev => ({ ...prev, annualStartAge: e.target.value }))}
                        options={Array.from({length: 50}, (_, i) => ({ value: (65 + i).toString(), label: (65 + i).toString() }))}
                        className="text-black"
                      />
                    </div>

                    {/* Schedule as */}
                    <div className="space-y-2 mt-4">
                      <div className="text-black">i. Schedule as</div>
                      <Button onClick={() => handleSchedule('Loan Schedule')} className="ml-2">Schedule</Button>
                    </div>
                  </div>

                  {/* Premium Financing */}
                  <div className="space-y-2">
                    <div className="text-black font-semibold">c. Loan for paying the premium (Premium financing)</div>
                    <div className="space-y-2 pl-4">
                      <div className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          checked={loanModel.premiumFinancing.loanInterestPayment}
                          onChange={e => setLoanModel(prev => ({
                            ...prev,
                            premiumFinancing: {
                              ...prev.premiumFinancing,
                              loanInterestPayment: e.target.checked
                            }
                          }))}
                        />
                        <span className="text-black">Loan Interest Payment</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          checked={loanModel.premiumFinancing.capitalizeLoanInterest}
                          onChange={e => setLoanModel(prev => ({
                            ...prev,
                            premiumFinancing: {
                              ...prev.premiumFinancing,
                              capitalizeLoanInterest: e.target.checked
                            }
                          }))}
                        />
                        <span className="text-black">Capitalize Loan interest</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          checked={loanModel.premiumFinancing.payOutOfPocket}
                          onChange={e => setLoanModel(prev => ({
                            ...prev,
                            premiumFinancing: {
                              ...prev.premiumFinancing,
                              payOutOfPocket: e.target.checked
                            }
                          }))}
                        />
                        <span className="text-black">Pay out of pocket</span>
                      </div>
                    </div>
                  </div>
                </div>
              )}
              <div className="flex items-center space-x-4 mt-2">
                <input type="checkbox" checked={!loanModel.enabled} onChange={e => setLoanModel(prev => ({ ...prev, enabled: !e.target.checked }))} />
                <span className="font-semibold text-black">No, do not model policy loans.</span>
              </div>
            </div>
          </Card>


          {/* Save and Reset Buttons (refactored to match AsIsPage) */}
          <div className="flex flex-wrap gap-4 justify-center mt-8">
            <Button onClick={handleSaveAll}
              variant="primary"
              className="flex items-center space-x-2 bg-indigo-600 hover:bg-indigo-700 text-white shadow-lg border-none px-8 py-3 text-lg font-semibold">
              <Save className="w-4 h-4" />
              <span>Save All Selected Scenarios</span>
            </Button>
            <Button onClick={handleResetScenarios}
              variant="primary"
              className="flex items-center space-x-2 bg-indigo-600 hover:bg-indigo-700 text-white shadow-lg border-none px-8 py-3 text-lg font-semibold">
              <RotateCcw className="w-4 h-4" />
              <span>Reset Scenarios</span>
            </Button>
            <Button
              onClick={() => alert('Proceed to next step (e.g., Loan Repayment Illustration)')}
              className="flex items-center space-x-2">
              <span>Proceed to Loan Repayment Illustration</span>
              <ArrowRight className="w-4 h-4" />
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};

export default IncomePage;