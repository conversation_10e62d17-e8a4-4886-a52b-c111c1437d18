import { <PERSON><PERSON><PERSON> } from '../types';

const STORAGE_KEYS = {
  SCENARIOS: 'insuranceApp_scenarios',
  SELECTED_SCENARIOS: 'insuranceApp_selectedScenarios',
} as const;

export class LocalStorageService {
  /**
   * Get all scenarios from localStorage
   */
  static getScenarios(): Scenario[] {
    try {
      const stored = localStorage.getItem(STORAGE_KEYS.SCENARIOS);
      if (!stored) return [];
      
      const scenarios = JSON.parse(stored);
      // Convert date strings back to Date objects
      return scenarios.map((scenario: any) => ({
        ...scenario,
        createdAt: new Date(scenario.createdAt),
        updatedAt: new Date(scenario.updatedAt),
      }));
    } catch (error) {
      console.error('Error loading scenarios from localStorage:', error);
      return [];
    }
  }

  /**
   * Get selected scenario IDs from localStorage
   */
  static getSelectedScenarios(): string[] {
    try {
      const stored = localStorage.getItem(STORAGE_KEYS.SELECTED_SCENARIOS);
      return stored ? JSON.parse(stored) : [];
    } catch (error) {
      console.error('Error loading selected scenarios from localStorage:', error);
      return [];
    }
  }

  /**
   * Save scenarios to localStorage
   */
  static saveScenarios(scenarios: Scenario[]): void {
    try {
      localStorage.setItem(STORAGE_KEYS.SCENARIOS, JSON.stringify(scenarios));
    } catch (error) {
      console.error('Error saving scenarios to localStorage:', error);
      throw new Error('Failed to save scenarios to localStorage');
    }
  }

  /**
   * Save selected scenario IDs to localStorage
   */
  static saveSelectedScenarios(selectedIds: string[]): void {
    try {
      localStorage.setItem(STORAGE_KEYS.SELECTED_SCENARIOS, JSON.stringify(selectedIds));
    } catch (error) {
      console.error('Error saving selected scenarios to localStorage:', error);
      throw new Error('Failed to save selected scenarios to localStorage');
    }
  }

  /**
   * Create a new scenario and save to localStorage
   */
  static createScenario(scenario: Omit<Scenario, 'id'>): string {
    try {
      const scenarios = this.getScenarios();
      const newId = Date.now().toString() + Math.random().toString(36).substr(2, 9);
      const newScenario: Scenario = {
        ...scenario,
        id: newId,
        createdAt: new Date(),
        updatedAt: new Date(),
      };
      
      scenarios.push(newScenario);
      this.saveScenarios(scenarios);
      
      return newId;
    } catch (error) {
      console.error('Error creating scenario:', error);
      throw new Error('Failed to create scenario');
    }
  }

  /**
   * Update an existing scenario
   */
  static updateScenario(id: string, updates: Partial<Scenario>): void {
    try {
      const scenarios = this.getScenarios();
      const index = scenarios.findIndex(scenario => scenario.id === id);
      
      if (index === -1) {
        throw new Error(`Scenario with id ${id} not found`);
      }
      
      scenarios[index] = {
        ...scenarios[index],
        ...updates,
        id, // Ensure ID doesn't change
        updatedAt: new Date(),
      };
      
      this.saveScenarios(scenarios);
    } catch (error) {
      console.error('Error updating scenario:', error);
      throw new Error('Failed to update scenario');
    }
  }

  /**
   * Delete a scenario by ID
   */
  static deleteScenario(id: string): void {
    try {
      const scenarios = this.getScenarios();
      const filteredScenarios = scenarios.filter(scenario => scenario.id !== id);
      
      if (filteredScenarios.length === scenarios.length) {
        throw new Error(`Scenario with id ${id} not found`);
      }
      
      this.saveScenarios(filteredScenarios);
      
      // Also remove from selected scenarios if present
      const selectedScenarios = this.getSelectedScenarios();
      const updatedSelected = selectedScenarios.filter(selectedId => selectedId !== id);
      if (updatedSelected.length !== selectedScenarios.length) {
        this.saveSelectedScenarios(updatedSelected);
      }
    } catch (error) {
      console.error('Error deleting scenario:', error);
      throw new Error('Failed to delete scenario');
    }
  }

  /**
   * Delete multiple scenarios by IDs
   */
  static deleteMultipleScenarios(ids: string[]): void {
    try {
      const scenarios = this.getScenarios();
      const filteredScenarios = scenarios.filter(scenario => !ids.includes(scenario.id));
      
      this.saveScenarios(filteredScenarios);
      
      // Also remove from selected scenarios if present
      const selectedScenarios = this.getSelectedScenarios();
      const updatedSelected = selectedScenarios.filter(selectedId => !ids.includes(selectedId));
      this.saveSelectedScenarios(updatedSelected);
    } catch (error) {
      console.error('Error deleting multiple scenarios:', error);
      throw new Error('Failed to delete scenarios');
    }
  }

  /**
   * Clear all scenarios and selected scenarios
   */
  static clearAllScenarios(): void {
    try {
      localStorage.removeItem(STORAGE_KEYS.SCENARIOS);
      localStorage.removeItem(STORAGE_KEYS.SELECTED_SCENARIOS);
    } catch (error) {
      console.error('Error clearing scenarios:', error);
      throw new Error('Failed to clear scenarios');
    }
  }

  /**
   * Get scenario by ID
   */
  static getScenarioById(id: string): Scenario | null {
    try {
      const scenarios = this.getScenarios();
      return scenarios.find(scenario => scenario.id === id) || null;
    } catch (error) {
      console.error('Error getting scenario by ID:', error);
      return null;
    }
  }

  /**
   * Check if localStorage is available
   */
  static isAvailable(): boolean {
    try {
      const test = '__localStorage_test__';
      localStorage.setItem(test, test);
      localStorage.removeItem(test);
      return true;
    } catch {
      return false;
    }
  }
}
