import React, { useState } from 'react';
import Card from '../common/Card';
import Button from '../common/Button';
import Input from '../common/Input';
import { Save, RotateCcw } from 'lucide-react';
import { useDashboard } from '../../contexts/DashboardContext';

const InterestRatePage: React.FC = () => {
  const { selectedCustomerData, selectedPolicyData, setActiveTab } = useDashboard();

  // New simplified scenario state
  const [modelScenarios, setModelScenarios] = useState(false);
  const [currentRate, setCurrentRate] = useState('');
  const [guaranteedMinRate, setGuaranteedMinRate] = useState('');
  const [stressRate, setStressRate] = useState('');
  const [userDefinedRates, setUserDefinedRates] = useState('');

  // Reset all scenario state
  const handleResetScenarios = () => {
    setModelScenarios(false);
    setCurrentRate('');
    setGuaranteedMinRate('');
    setStressRate('');
    setUserDefinedRates('');
    alert('All interest rate scenarios have been reset!');
  };

  // Placeholder for schedule button
  const handleSchedule = () => {
    alert('Open schedule editor for User-defined Rates');
  };

  return (
    <div className="space-y-6">
      {/* <div>
        <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">INTEREST RATE ILLUSTRATION</h1>
        <p className="text-gray-600 dark:text-gray-400">Configure interest/crediting rate scenarios for the selected policy.</p>
      </div> */}

      {/* Show message if no policy is selected */}
      {(!selectedCustomerData || !selectedPolicyData) ? (
        <Card className="bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-yellow-100 dark:bg-yellow-900/40 rounded-full flex items-center justify-center">
              <span className="text-yellow-600 dark:text-yellow-400 text-sm">!</span>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-yellow-800 dark:text-yellow-200">No Policy Selected</h3>
              <p className="text-yellow-700 dark:text-yellow-300">
                Please go to the Policy Selection tab first to search and select a customer policy before configuring the Interest Rate illustration.
              </p>
              <Button
                onClick={() => setActiveTab('policy-selection')}
                variant="outline"
                className="mt-3 border-yellow-300 text-yellow-700 hover:bg-yellow-100 dark:border-yellow-600 dark:text-yellow-300 dark:hover:bg-yellow-900/40"
              >
                Go to Policy Selection
              </Button>
            </div>
          </div>
        </Card>
      ) : (
        <>
          {/* New Simplified Interest Rate Scenarios */}
          <Card className="mb-8">
            <h2 className="text-xl font-bold mb-4 text-black">1. Do you want to model different interest / crediting rate scenarios for the policy?</h2>
            <div className="space-y-4">
              <div className="flex items-center space-x-4">
                <input
                  type="radio"
                  name="modelScenarios"
                  checked={modelScenarios}
                  onChange={e => setModelScenarios(e.target.checked)}
                />
                <span className="font-semibold text-black">Yes, model different interest/crediting rate scenarios for the policy.</span>
              </div>

              {modelScenarios && (
                <div className="space-y-4 pl-6">
                  <div className="flex flex-col space-y-3">
                    <Input
                      label="Current interest/crediting rate"
                      value={currentRate}
                      onChange={e => setCurrentRate(e.target.value)}
                      placeholder="____%"
                      className="text-black placeholder-black w-64"
                    />
                    <Input
                      label="Guaranteed minimum rate"
                      value={guaranteedMinRate}
                      onChange={e => setGuaranteedMinRate(e.target.value)}
                      placeholder="____%"
                      className="text-black placeholder-black w-64"
                    />
                    <Input
                      label="Stress scenario rate"
                      value={stressRate}
                      onChange={e => setStressRate(e.target.value)}
                      placeholder="____%"
                      className="text-black placeholder-black w-64"
                    />
                    <div className="flex items-center space-x-2">
                      <Input
                        label="User-defined Rates"
                        value={userDefinedRates}
                        onChange={e => setUserDefinedRates(e.target.value)}
                        placeholder="Schedule"
                        className="text-black placeholder-black w-64"
                      />
                      <Button onClick={handleSchedule} className="ml-2">Schedule</Button>
                    </div>
                  </div>
                </div>
              )}

              <div className="flex items-center space-x-4 mt-2">
                <input
                  type="radio"
                  name="modelScenarios"
                  checked={!modelScenarios}
                  onChange={() => setModelScenarios(false)}
                />
                <span className="font-semibold text-black">No, do not include interest/crediting rate scenario modelling.</span>
              </div>
            </div>
          </Card>

          {/* Action Buttons */}
          <div className="flex flex-wrap gap-4 justify-center">
            <Button
              onClick={() => alert('Interest rate scenario configuration saved successfully!')}
              variant="primary"
              className="flex items-center space-x-2 bg-indigo-600 hover:bg-indigo-700 text-white shadow-lg border-none"
            >
              <Save className="w-4 h-4" />
              <span>Save Interest Rate Illustration</span>
            </Button>
            <Button
              onClick={handleResetScenarios}
              variant="primary"
              className="flex items-center space-x-2 bg-indigo-600 hover:bg-indigo-700 text-white shadow-lg border-none"
            >
              <RotateCcw className="w-4 h-4" />
              <span>Reset Scenarios</span>
            </Button>
          </div>
        </>
      )}
    </div>
  );
};

export default InterestRatePage;