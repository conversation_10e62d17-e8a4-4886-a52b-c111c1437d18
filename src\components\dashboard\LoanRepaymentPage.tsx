import React, { useState } from 'react';
import Card from '../common/Card';
import Button from '../common/Button';
import Select from '../common/Select';
import Input from '../common/Input';
import { Save, RotateCcw } from 'lucide-react';
import { useDashboard } from '../../contexts/DashboardContext';

const LoanRepaymentPage: React.FC = () => {
  const { selectedCustomerData, selectedPolicyData, setActiveTab } = useDashboard();

  // New simplified loan repayment strategy state
  const [modelLoanRepayment, setModelLoanRepayment] = useState(false);
  const [repaymentType, setRepaymentType] = useState('');
  const [newLoanAmount, setNewLoanAmount] = useState('');
  const [startingAge, setStartingAge] = useState('');
  const [lumpSumAmount, setLumpSumAmount] = useState('');
  const [lumpSumTiming, setLumpSumTiming] = useState('now');

  // Reset all scenario state
  const handleResetScenarios = () => {
    setModelLoanRepayment(false);
    setRepaymentType('');
    setNewLoanAmount('');
    setStartingAge('');
    setLumpSumAmount('');
    setLumpSumTiming('now');
    alert('All loan repayment scenarios have been reset!');
  };

  // Placeholder for schedule button
  const handleSchedule = () => {
    alert('Open schedule editor for Vary the Amount by Age');
  };

  const handleSaveLoanRepayment = () => {
    alert('Loan repayment strategy configuration saved successfully!');
  };

  return (
    <div className="space-y-6">
      {/* Show message if no policy is selected */}
      {(!selectedCustomerData || !selectedPolicyData) ? (
        <Card className="bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-yellow-100 dark:bg-yellow-900/40 rounded-full flex items-center justify-center">
              <span className="text-yellow-600 dark:text-yellow-400 text-sm">!</span>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-yellow-800 dark:text-yellow-200">No Policy Selected</h3>
              <p className="text-yellow-700 dark:text-yellow-300">
                Please go to the Policy Selection tab first to search and select a customer policy before configuring the Loan Repayment illustration.
              </p>
              <Button
                onClick={() => setActiveTab('policy-selection')}
                variant="outline"
                className="mt-3 border-yellow-300 text-yellow-700 hover:bg-yellow-100 dark:border-yellow-600 dark:text-yellow-300 dark:hover:bg-yellow-900/40"
              >
                Go to Policy Selection
              </Button>
            </div>
          </div>
        </Card>
      ) : (
        <>
          {/* New Loan Repayment Strategy */}
          <Card className="mb-8">
            <h2 className="text-xl font-bold mb-4 text-black">1. Do you want to model a loan repayment strategy (scheduled or lump sum)?</h2>
            <div className="space-y-4">
              <div className="flex items-center space-x-4">
                <input
                  type="radio"
                  name="modelLoanRepayment"
                  checked={modelLoanRepayment}
                  onChange={e => setModelLoanRepayment(e.target.checked)}
                />
                <span className="font-semibold text-black">Yes, model a loan repayment strategy.</span>
              </div>

              {modelLoanRepayment && (
                <div className="space-y-4 pl-6">
                  {/* Change to New Loan Repayment amount */}
                  <div className="flex items-center space-x-2">
                    <input
                      type="radio"
                      name="repaymentType"
                      value="change-amount"
                      checked={repaymentType === 'change-amount'}
                      onChange={e => setRepaymentType(e.target.value)}
                    />
                    <span className="text-black">Change to New Loan Repayment amount</span>
                    <Input
                      value={newLoanAmount}
                      onChange={e => setNewLoanAmount(e.target.value)}
                      placeholder="____"
                      className="text-black placeholder-black w-24"
                    />
                    <span className="text-black">now or starting from age</span>
                    <Select
                      value={startingAge}
                      onChange={e => setStartingAge(e.target.value)}
                      options={[
                        { value: '', label: '<Year Combo>' },
                        { value: '30', label: '30' },
                        { value: '35', label: '35' },
                        { value: '40', label: '40' },
                        { value: '45', label: '45' },
                        { value: '50', label: '50' },
                        { value: '55', label: '55' },
                        { value: '60', label: '60' },
                        { value: '65', label: '65' },
                        { value: '70', label: '70' },
                      ]}
                      className="text-black w-32"
                    />
                  </div>

                  {/* Vary the Amount by Age */}
                  <div className="flex items-center space-x-2">
                    <input
                      type="radio"
                      name="repaymentType"
                      value="vary-by-age"
                      checked={repaymentType === 'vary-by-age'}
                      onChange={e => setRepaymentType(e.target.value)}
                    />
                    <span className="text-black">Vary the Amount by Age</span>
                    <Button onClick={handleSchedule} className="ml-2">
                      SCHEDULE BUTTON
                    </Button>
                  </div>

                  {/* Lump Sum (One-time premium) */}
                  <div className="flex items-center space-x-2">
                    <input
                      type="radio"
                      name="repaymentType"
                      value="lump-sum"
                      checked={repaymentType === 'lump-sum'}
                      onChange={e => setRepaymentType(e.target.value)}
                    />
                    <span className="text-black">Lump Sum (One-time premium): $</span>
                    <Input
                      value={lumpSumAmount}
                      onChange={e => setLumpSumAmount(e.target.value)}
                      placeholder="__________"
                      className="text-black placeholder-black w-32"
                    />
                    <div className="flex items-center space-x-2">
                      <input
                        type="radio"
                        name="lumpSumTiming"
                        value="now"
                        checked={lumpSumTiming === 'now'}
                        onChange={e => setLumpSumTiming(e.target.value)}
                      />
                      <span className="text-black">now</span>
                    </div>
                  </div>

                  {/* Interest only Payment */}
                  <div className="flex items-center space-x-2">
                    <input
                      type="radio"
                      name="repaymentType"
                      value="interest-only"
                      checked={repaymentType === 'interest-only'}
                      onChange={e => setRepaymentType(e.target.value)}
                    />
                    <span className="text-black">Interest only Payment</span>
                  </div>
                </div>
              )}

              <div className="flex items-center space-x-4 mt-2">
                <input
                  type="radio"
                  name="modelLoanRepayment"
                  checked={!modelLoanRepayment}
                  onChange={() => setModelLoanRepayment(false)}
                />
                <span className="font-semibold text-black">No, do not model loan repayment strategy.</span>
              </div>
            </div>
          </Card>
          {/* Action Buttons */}
          <div className="flex flex-wrap gap-4 justify-center">
            <Button
              onClick={handleSaveLoanRepayment}
              variant="primary"
              className="flex items-center space-x-2 bg-indigo-600 hover:bg-indigo-700 text-white shadow-lg border-none"
            >
              <Save className="w-4 h-4" />
              <span>Save Loan Repayment Illustration</span>
            </Button>
            <Button
              onClick={handleResetScenarios}
              variant="primary"
              className="flex items-center space-x-2 bg-indigo-600 hover:bg-indigo-700 text-white shadow-lg border-none"
            >
              <RotateCcw className="w-4 h-4" />
              <span>Reset Scenarios</span>
            </Button>
          </div>
        </>
      )}
    </div>
  );
};

export default LoanRepaymentPage;